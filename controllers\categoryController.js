const Category = require('../models/Categories');
const Product = require('../models/Products');

// @desc    Get all categories
// @route   GET /api/categories
// @access  Public
const getCategories = async (req, res) => {
    try {
        const { page = 1, limit = 10, search, sort = 'name' } = req.query;

        // Build query object
        const query = {};
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        // Build sort object
        const sortObject = {};
        if (sort === 'name') {
            sortObject.name = 1;
        } else if (sort === 'newest') {
            sortObject.createdAt = -1;
        } else if (sort === 'oldest') {
            sortObject.createdAt = 1;
        } else {
            sortObject.name = 1; // Default sort
        }

        const categories = await Category.find(query)
            .sort(sortObject)
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Category.countDocuments(query);

        res.json({
            success: true,
            categories,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching categories',
            error: error.message
        });
    }
};

// @desc    Get category by ID
// @route   GET /api/categories/:id
// @access  Public
const getCategoryById = async (req, res) => {
    try {
        const category = await Category.findById(req.params.id);

        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        // Get product count for this category
        const productCount = await Product.countDocuments({ 
            categoryId: req.params.id,
            active: true 
        });

        res.json({
            success: true,
            category: {
                ...category.toObject(),
                productCount
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching category',
            error: error.message
        });
    }
};

// @desc    Create new category
// @route   POST /api/categories
// @access  Private/Admin
const createCategory = async (req, res) => {
    try {
        const { name, description } = req.body;

        // Validate required fields
        if (!name) {
            return res.status(400).json({
                success: false,
                message: 'Category name is required'
            });
        }

        // Check if category already exists
        const existingCategory = await Category.findOne({ 
            name: { $regex: new RegExp(`^${name}$`, 'i') } 
        });

        if (existingCategory) {
            return res.status(400).json({
                success: false,
                message: 'Category with this name already exists'
            });
        }

        const category = new Category({
            name: name.trim(),
            description: description || ''
        });

        await category.save();

        res.status(201).json({
            success: true,
            message: 'Category created successfully',
            category
        });
    } catch (error) {
        if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                message: 'Category with this name already exists'
            });
        }
        res.status(500).json({
            success: false,
            message: 'Server error creating category',
            error: error.message
        });
    }
};

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private/Admin
const updateCategory = async (req, res) => {
    try {
        const { name, description } = req.body;

        const category = await Category.findById(req.params.id);
        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        // If name is being updated, check for duplicates
        if (name && name.trim() !== category.name) {
            const existingCategory = await Category.findOne({ 
                name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
                _id: { $ne: req.params.id }
            });

            if (existingCategory) {
                return res.status(400).json({
                    success: false,
                    message: 'Category with this name already exists'
                });
            }
        }

        // Update fields if provided
        if (name !== undefined) category.name = name.trim();
        if (description !== undefined) category.description = description;

        await category.save();

        res.json({
            success: true,
            message: 'Category updated successfully',
            category
        });
    } catch (error) {
        if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                message: 'Category with this name already exists'
            });
        }
        res.status(500).json({
            success: false,
            message: 'Server error updating category',
            error: error.message
        });
    }
};

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private/Admin
const deleteCategory = async (req, res) => {
    try {
        const category = await Category.findById(req.params.id);
        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        // Check if category has products
        const productCount = await Product.countDocuments({ categoryId: req.params.id });
        if (productCount > 0) {
            return res.status(400).json({
                success: false,
                message: `Cannot delete category. ${productCount} products are using this category`
            });
        }

        await Category.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Category deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error deleting category',
            error: error.message
        });
    }
};

// @desc    Get categories with product count
// @route   GET /api/categories/with-products
// @access  Public
const getCategoriesWithProductCount = async (req, res) => {
    try {
        const { limit = 20 } = req.query;

        const categories = await Category.aggregate([
            {
                $lookup: {
                    from: 'products',
                    localField: '_id',
                    foreignField: 'categoryId',
                    as: 'products'
                }
            },
            {
                $addFields: {
                    productCount: {
                        $size: {
                            $filter: {
                                input: '$products',
                                cond: { $eq: ['$$this.active', true] }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    name: 1,
                    description: 1,
                    createdAt: 1,
                    updatedat: 1,
                    productCount: 1
                }
            },
            {
                $sort: { name: 1 }
            },
            {
                $limit: parseInt(limit)
            }
        ]);

        res.json({
            success: true,
            categories
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching categories with product count',
            error: error.message
        });
    }
};

// @desc    Get category statistics
// @route   GET /api/categories/stats
// @access  Private/Admin
const getCategoryStats = async (req, res) => {
    try {
        const totalCategories = await Category.countDocuments();

        // Get categories with product counts
        const categoryStats = await Category.aggregate([
            {
                $lookup: {
                    from: 'products',
                    localField: '_id',
                    foreignField: 'categoryId',
                    as: 'products'
                }
            },
            {
                $addFields: {
                    totalProducts: { $size: '$products' },
                    activeProducts: {
                        $size: {
                            $filter: {
                                input: '$products',
                                cond: { $eq: ['$$this.active', true] }
                            }
                        }
                    },
                    inactiveProducts: {
                        $size: {
                            $filter: {
                                input: '$products',
                                cond: { $eq: ['$$this.active', false] }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    name: 1,
                    description: 1,
                    totalProducts: 1,
                    activeProducts: 1,
                    inactiveProducts: 1,
                    createdAt: 1
                }
            },
            {
                $sort: { totalProducts: -1 }
            }
        ]);

        // Get categories with no products
        const emptyCategories = categoryStats.filter(cat => cat.totalProducts === 0);

        // Get top categories by product count
        const topCategories = categoryStats.slice(0, 5);

        // Get recent categories
        const recentCategories = await Category.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .select('name description createdAt');

        res.json({
            success: true,
            stats: {
                totalCategories,
                categoriesWithProducts: categoryStats.length - emptyCategories.length,
                emptyCategories: emptyCategories.length,
                topCategories,
                recentCategories,
                categoryStats
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching category statistics',
            error: error.message
        });
    }
};

// @desc    Get products by category
// @route   GET /api/categories/:id/products
// @access  Public
const getProductsByCategory = async (req, res) => {
    try {
        const { page = 1, limit = 10, active, featured, sort = 'name' } = req.query;

        // Check if category exists
        const category = await Category.findById(req.params.id);
        if (!category) {
            return res.status(404).json({
                success: false,
                message: 'Category not found'
            });
        }

        // Build query object
        const query = { categoryId: req.params.id };
        if (active !== undefined) query.active = active === 'true';
        if (featured !== undefined) query.isFeatured = featured === 'true';

        // Build sort object
        const sortObject = {};
        if (sort === 'name') {
            sortObject.name = 1;
        } else if (sort === 'price_low') {
            sortObject.price = 1;
        } else if (sort === 'price_high') {
            sortObject.price = -1;
        } else if (sort === 'newest') {
            sortObject.createdAt = -1;
        } else if (sort === 'rating') {
            sortObject.averageRating = -1;
        } else {
            sortObject.name = 1; // Default sort
        }

        const products = await Product.find(query)
            .sort(sortObject)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .select('name price images averageRating totalReviews isFeatured inStock active');

        const total = await Product.countDocuments(query);

        res.json({
            success: true,
            category: {
                id: category._id,
                name: category.name,
                description: category.description
            },
            products,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching products by category',
            error: error.message
        });
    }
};

// @desc    Bulk delete categories
// @route   DELETE /api/categories/bulk-delete
// @access  Private/Admin
const bulkDeleteCategories = async (req, res) => {
    try {
        const { categoryIds } = req.body;

        if (!categoryIds || !Array.isArray(categoryIds) || categoryIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Category IDs array is required'
            });
        }

        // Check if any categories have products
        const categoriesWithProducts = await Category.aggregate([
            {
                $match: { _id: { $in: categoryIds.map(id => mongoose.Types.ObjectId(id)) } }
            },
            {
                $lookup: {
                    from: 'products',
                    localField: '_id',
                    foreignField: 'categoryId',
                    as: 'products'
                }
            },
            {
                $match: { 'products.0': { $exists: true } }
            },
            {
                $project: { name: 1, productCount: { $size: '$products' } }
            }
        ]);

        if (categoriesWithProducts.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'Cannot delete categories that have products',
                categoriesWithProducts
            });
        }

        const result = await Category.deleteMany({ _id: { $in: categoryIds } });

        res.json({
            success: true,
            message: `${result.deletedCount} categories deleted successfully`,
            deleted: result.deletedCount
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error deleting categories',
            error: error.message
        });
    }
};

module.exports = {
    getCategories,
    getCategoryById,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoriesWithProductCount,
    getCategoryStats,
    getProductsByCategory,
    bulkDeleteCategories
};