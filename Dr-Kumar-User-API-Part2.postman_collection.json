{"info": {"_postman_id": "user-api-part2-collection-id", "name": "Dr Kumar User API Collection - Part 2", "description": "Additional user endpoints for Dr Kumar API including advanced cart features and order management. All routes require user authentication.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "user_token", "value": "", "type": "string"}], "item": [{"name": "Advanced Cart Features", "description": "Advanced shopping cart management endpoints", "item": [{"name": "Get Cart Summary", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/cart/summary", "host": ["{{base_url}}"], "path": ["user", "cart", "summary"]}, "description": "Get cart summary with total items, subtotal, taxes, and final total amount."}}, {"name": "Validate <PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/cart/validate", "host": ["{{base_url}}"], "path": ["user", "cart", "validate"]}, "description": "Validate cart items for availability, pricing changes, and stock status before checkout."}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"guestCartItems\": [\n    {\n      \"productId\": \"product_id_1\",\n      \"quantity\": 2\n    },\n    {\n      \"productId\": \"product_id_2\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/user/cart/merge", "host": ["{{base_url}}"], "path": ["user", "cart", "merge"]}, "description": "Merge guest cart items with user's existing cart after login. Useful for preserving cart state across sessions."}}, {"name": "Get Cart Count", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/cart/count", "host": ["{{base_url}}"], "path": ["user", "cart", "count"]}, "description": "Get total number of items in the cart. Useful for displaying cart badge count."}}]}, {"name": "Orders", "description": "Order management endpoints for users", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"product\": \"product_id_1\",\n      \"quantity\": 2,\n      \"price\": 99.99\n    },\n    {\n      \"product\": \"product_id_2\",\n      \"quantity\": 1,\n      \"price\": 149.99\n    }\n  ],\n  \"shippingAddress\": {\n    \"addressLine1\": \"123 Main Street\",\n    \"addressLine2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"postalCode\": \"10001\",\n    \"country\": \"USA\"\n  }\n}"}, "url": {"raw": "{{base_url}}/user/orders", "host": ["{{base_url}}"], "path": ["user", "orders"]}, "description": "Create a new order from cart items or specified items. If shippingAddress is not provided, user's default address will be used."}}, {"name": "Get User Orders", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/orders?page=1&limit=10&status=pending", "host": ["{{base_url}}"], "path": ["user", "orders"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of orders per page"}, {"key": "status", "value": "pending", "description": "Filter by order status"}]}, "description": "Get all orders for the current user with pagination and filtering options. Supports filtering by status, date range, etc."}}, {"name": "Get Order by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/orders/:id", "host": ["{{base_url}}"], "path": ["user", "orders", ":id"], "variable": [{"key": "id", "value": "order_id_here"}]}, "description": "Get detailed information about a specific order including items, shipping details, and status history."}}, {"name": "Cancel Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\",\n  \"comments\": \"No longer need these items\"\n}"}, "url": {"raw": "{{base_url}}/user/orders/:id/cancel", "host": ["{{base_url}}"], "path": ["user", "orders", ":id", "cancel"], "variable": [{"key": "id", "value": "order_id_here"}]}, "description": "Cancel an order. Only orders with 'pending' or 'confirmed' status can be cancelled. Reason is optional but recommended."}}, {"name": "Update Shipping Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shippingAddress\": {\n    \"addressLine1\": \"456 Updated Street\",\n    \"addressLine2\": \"Suite 200\",\n    \"city\": \"Updated City\",\n    \"state\": \"CA\",\n    \"postalCode\": \"90210\",\n    \"country\": \"USA\"\n  }\n}"}, "url": {"raw": "{{base_url}}/user/orders/:id/shipping-address", "host": ["{{base_url}}"], "path": ["user", "orders", ":id", "shipping-address"], "variable": [{"key": "id", "value": "order_id_here"}]}, "description": "Update shipping address for an order. Only allowed for orders that haven't been shipped yet."}}]}]}