{"info": {"_postman_id": "admin-api-collection-id", "name": "Dr <PERSON> Admin API Collection", "description": "Complete API collection for <PERSON> Kumar Admin endpoints. All routes require admin authentication with JWT token in Authorization header as 'Bearer <token>'.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}], "item": [{"name": "Admin Banners", "description": "Banner management endpoints for administrators", "item": [{"name": "Create Banner", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Summer Sale Banner\",\n  \"description\": \"Get 50% off on all products\",\n  \"imageUrl\": \"https://example.com/banner.jpg\",\n  \"link\": \"https://example.com/sale\",\n  \"priority\": 1,\n  \"startDate\": \"2024-01-01T00:00:00.000Z\",\n  \"endDate\": \"2024-12-31T23:59:59.000Z\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/admin/banners", "host": ["{{base_url}}"], "path": ["admin", "banners"]}, "description": "Create a new banner. Requires title and imageUrl. Optional fields include description, link, priority, startDate, endDate, and isActive."}}, {"name": "Get All Banners", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/banners", "host": ["{{base_url}}"], "path": ["admin", "banners"]}, "description": "Retrieve all banners with pagination and filtering options."}}, {"name": "Get Banner by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/banners/:id", "host": ["{{base_url}}"], "path": ["admin", "banners", ":id"], "variable": [{"key": "id", "value": "banner_id_here"}]}, "description": "Get a specific banner by its ID."}}, {"name": "Update Banner", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Banner Title\",\n  \"description\": \"Updated description\",\n  \"imageUrl\": \"https://example.com/updated-banner.jpg\",\n  \"link\": \"https://example.com/updated-link\",\n  \"priority\": 2,\n  \"isActive\": false\n}"}, "url": {"raw": "{{base_url}}/admin/banners/:id", "host": ["{{base_url}}"], "path": ["admin", "banners", ":id"], "variable": [{"key": "id", "value": "banner_id_here"}]}, "description": "Update an existing banner by ID. All fields are optional."}}, {"name": "Delete Banner", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/admin/banners/:id", "host": ["{{base_url}}"], "path": ["admin", "banners", ":id"], "variable": [{"key": "id", "value": "banner_id_here"}]}, "description": "Delete a banner by ID. This action is irreversible."}}, {"name": "Toggle Banner Status", "request": {"method": "PUT", "url": {"raw": "{{base_url}}/admin/banners/:id/toggle-active", "host": ["{{base_url}}"], "path": ["admin", "banners", ":id", "toggle-active"], "variable": [{"key": "id", "value": "banner_id_here"}]}, "description": "Toggle the active status of a banner (active/inactive)."}}, {"name": "Update Banner Priority", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"priority\": 5\n}"}, "url": {"raw": "{{base_url}}/admin/banners/:id/priority", "host": ["{{base_url}}"], "path": ["admin", "banners", ":id", "priority"], "variable": [{"key": "id", "value": "banner_id_here"}]}, "description": "Update the priority of a banner. Higher numbers indicate higher priority."}}, {"name": "Get Banner Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/banners/stats", "host": ["{{base_url}}"], "path": ["admin", "banners", "stats"]}, "description": "Get comprehensive statistics about banners including total count, active/inactive counts, etc."}}, {"name": "Get Expiring Soon Banners", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/banners/expiring-soon", "host": ["{{base_url}}"], "path": ["admin", "banners", "expiring-soon"]}, "description": "Get banners that are expiring soon (within next 7 days)."}}, {"name": "Get Active Banners", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/banners/active", "host": ["{{base_url}}"], "path": ["admin", "banners", "active"]}, "description": "Get all currently active banners."}}, {"name": "Bulk Update Banners", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bannerIds\": [\"banner_id_1\", \"banner_id_2\"],\n  \"updates\": {\n    \"isActive\": false,\n    \"priority\": 1\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/banners/bulk-update", "host": ["{{base_url}}"], "path": ["admin", "banners", "bulk-update"]}, "description": "Update multiple banners at once. Provide array of banner IDs and the updates to apply."}}]}, {"name": "Admin Categories", "description": "Category management endpoints for administrators", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electronics\",\n  \"description\": \"Electronic devices and accessories\",\n  \"image\": \"https://example.com/electronics.jpg\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/admin/categories", "host": ["{{base_url}}"], "path": ["admin", "categories"]}, "description": "Create a new product category. Name is required, other fields are optional."}}, {"name": "Get All Categories", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/categories", "host": ["{{base_url}}"], "path": ["admin", "categories"]}, "description": "Retrieve all categories with optional filtering and pagination."}}, {"name": "Get Category by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/categories/:id", "host": ["{{base_url}}"], "path": ["admin", "categories", ":id"], "variable": [{"key": "id", "value": "category_id_here"}]}, "description": "Get a specific category by its ID."}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Electronics\",\n  \"description\": \"Updated description for electronics\",\n  \"image\": \"https://example.com/updated-electronics.jpg\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{base_url}}/admin/categories/:id", "host": ["{{base_url}}"], "path": ["admin", "categories", ":id"], "variable": [{"key": "id", "value": "category_id_here"}]}, "description": "Update an existing category by ID. All fields are optional."}}, {"name": "Delete Category", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/admin/categories/:id", "host": ["{{base_url}}"], "path": ["admin", "categories", ":id"], "variable": [{"key": "id", "value": "category_id_here"}]}, "description": "Delete a category by ID. This will also affect associated products."}}, {"name": "Get Categories with Product Count", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/categories/productcount", "host": ["{{base_url}}"], "path": ["admin", "categories", "productcount"]}, "description": "Get all categories along with the count of products in each category."}}, {"name": "Get Products by Category", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/categories/category-products?categoryId=category_id_here", "host": ["{{base_url}}"], "path": ["admin", "categories", "category-products"], "query": [{"key": "categoryId", "value": "category_id_here"}]}, "description": "Get all products belonging to a specific category."}}, {"name": "Get Category Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/categories/stats", "host": ["{{base_url}}"], "path": ["admin", "categories", "stats"]}, "description": "Get comprehensive statistics about categories including total count, active/inactive counts, etc."}}, {"name": "Bulk Delete Categories", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"categoryIds\": [\"category_id_1\", \"category_id_2\"]\n}"}, "url": {"raw": "{{base_url}}/admin/categories/bulk-delete", "host": ["{{base_url}}"], "path": ["admin", "categories", "bulk-delete"]}, "description": "Delete multiple categories at once. Provide array of category IDs."}}]}, {"name": "Admin Products", "description": "Product management endpoints for administrators", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"iPhone 15 Pro\",\n  \"description\": \"Latest iPhone with advanced features\",\n  \"categoryId\": \"category_id_here\",\n  \"price\": 999.99,\n  \"category\": \"Electronics\",\n  \"inStock\": true,\n  \"images\": [\n    {\n      \"url\": \"https://example.com/iphone15-1.jpg\",\n      \"altText\": \"iPhone 15 Pro front view\"\n    }\n  ],\n  \"isFeatured\": false\n}"}, "url": {"raw": "{{base_url}}/admin/products", "host": ["{{base_url}}"], "path": ["admin", "products"]}, "description": "Create a new product. Required fields: name, categoryId, price. Optional fields include description, category, inStock, images, isFeatured."}}, {"name": "Get All Products", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/products", "host": ["{{base_url}}"], "path": ["admin", "products"]}, "description": "Retrieve all products with pagination and filtering options."}}, {"name": "Get Product by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/products/:id", "host": ["{{base_url}}"], "path": ["admin", "products", ":id"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Get a specific product by its ID with full details."}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated iPhone 15 Pro\",\n  \"description\": \"Updated description\",\n  \"price\": 1099.99,\n  \"inStock\": true,\n  \"isFeatured\": true\n}"}, "url": {"raw": "{{base_url}}/admin/products/:id", "host": ["{{base_url}}"], "path": ["admin", "products", ":id"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Update an existing product by ID. All fields are optional."}}, {"name": "Delete Product", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/admin/products/:id", "host": ["{{base_url}}"], "path": ["admin", "products", ":id"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Delete a product by ID. This action is irreversible."}}, {"name": "Toggle Featured Status", "request": {"method": "PUT", "url": {"raw": "{{base_url}}/admin/products/:id/featured", "host": ["{{base_url}}"], "path": ["admin", "products", ":id", "featured"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Toggle the featured status of a product (featured/not featured)."}}, {"name": "Toggle Stock Status", "request": {"method": "PUT", "url": {"raw": "{{base_url}}/admin/products/:id/stock", "host": ["{{base_url}}"], "path": ["admin", "products", ":id", "stock"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Toggle the stock status of a product (in stock/out of stock)."}}, {"name": "Get Product Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/products/stats", "host": ["{{base_url}}"], "path": ["admin", "products", "stats"]}, "description": "Get comprehensive statistics about products including total count, featured count, stock status, etc."}}]}, {"name": "Admin Orders", "description": "Order management endpoints for administrators", "item": [{"name": "Get All Orders", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/orders", "host": ["{{base_url}}"], "path": ["admin", "orders"]}, "description": "Retrieve all orders with pagination and filtering options."}}, {"name": "Get Order by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/orders/:id", "host": ["{{base_url}}"], "path": ["admin", "orders", ":id"], "variable": [{"key": "id", "value": "order_id_here"}]}, "description": "Get a specific order by its ID with full details including items and user information."}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"shipped\"\n}"}, "url": {"raw": "{{base_url}}/admin/orders/:id/status", "host": ["{{base_url}}"], "path": ["admin", "orders", ":id", "status"], "variable": [{"key": "id", "value": "order_id_here"}]}, "description": "Update order status. Valid statuses: pending, confirmed, shipped, delivered, cancelled."}}, {"name": "Delete Order", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/admin/orders/:id", "host": ["{{base_url}}"], "path": ["admin", "orders", ":id"], "variable": [{"key": "id", "value": "order_id_here"}]}, "description": "Delete an order by ID. This action is irreversible."}}, {"name": "Get Order Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/orders/stats", "host": ["{{base_url}}"], "path": ["admin", "orders", "stats"]}, "description": "Get comprehensive statistics about orders including total count, status breakdown, revenue, etc."}}]}]}