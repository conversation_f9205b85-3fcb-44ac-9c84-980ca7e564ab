const Banner = require('../models/Banner');

// @desc    Get all banners
// @route   GET /api/banners
// @access  Public
const getBanners = async (req, res) => {
    try {
        const { page = 1, limit = 10, active, priority, search } = req.query;

        // Build query object
        const query = {};
        if (active !== undefined) query.isActive = active === 'true';
        if (priority !== undefined) query.priority = parseInt(priority);
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        const banners = await Banner.find(query)
            .sort({ priority: -1, createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Banner.countDocuments(query);

        res.json({
            success: true,
            banners,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching banners',
            error: error.message
        });
    }
};

// @desc    Get active banners (for public display)
// @route   GET /api/banners/active
// @access  Public
const getActiveBanners = async (req, res) => {
    try {
        const { limit = 10 } = req.query;
        const currentDate = new Date();

        const banners = await Banner.find({
            isActive: true,
            startDate: { $lte: currentDate },
            endDate: { $gte: currentDate }
        })
        .sort({ priority: -1, createdAt: -1 })
        .limit(limit * 1);

        res.json({
            success: true,
            banners
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching active banners',
            error: error.message
        });
    }
};

// @desc    Get banner by ID
// @route   GET /api/banners/:id
// @access  Public
const getBannerById = async (req, res) => {
    try {
        const banner = await Banner.findById(req.params.id);

        if (!banner) {
            return res.status(404).json({
                success: false,
                message: 'Banner not found'
            });
        }

        res.json({
            success: true,
            banner
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching banner',
            error: error.message
        });
    }
};

// @desc    Create new banner
// @route   POST /api/banners
// @access  Private/Admin
const createBanner = async (req, res) => {
    try {
        const {
            title,
            description,
            imageUrl,
            link,
            priority,
            startDate,
            endDate,
            isActive
        } = req.body;

        // Validate required fields
        if (!title || !imageUrl) {
            return res.status(400).json({
                success: false,
                message: 'Title and image URL are required'
            });
        }

        // Validate dates
        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
            return res.status(400).json({
                success: false,
                message: 'Start date cannot be after end date'
            });
        }

        const banner = new Banner({
            title,
            description,
            imageUrl,
            link,
            priority: priority || 0,
            startDate: startDate || Date.now(),
            endDate: endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default 30 days
            isActive: isActive !== undefined ? isActive : true
        });

        await banner.save();

        res.status(201).json({
            success: true,
            message: 'Banner created successfully',
            banner
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error creating banner',
            error: error.message
        });
    }
};

// @desc    Update banner
// @route   PUT /api/banners/:id
// @access  Private/Admin
const updateBanner = async (req, res) => {
    try {
        const {
            title,
            description,
            imageUrl,
            link,
            priority,
            startDate,
            endDate,
            isActive
        } = req.body;

        const banner = await Banner.findById(req.params.id);
        if (!banner) {
            return res.status(404).json({
                success: false,
                message: 'Banner not found'
            });
        }

        // Validate dates if provided
        const newStartDate = startDate ? new Date(startDate) : banner.startDate;
        const newEndDate = endDate ? new Date(endDate) : banner.endDate;

        if (newStartDate > newEndDate) {
            return res.status(400).json({
                success: false,
                message: 'Start date cannot be after end date'
            });
        }

        // Update fields if provided
        if (title !== undefined) banner.title = title;
        if (description !== undefined) banner.description = description;
        if (imageUrl !== undefined) banner.imageUrl = imageUrl;
        if (link !== undefined) banner.link = link;
        if (priority !== undefined) banner.priority = priority;
        if (startDate !== undefined) banner.startDate = newStartDate;
        if (endDate !== undefined) banner.endDate = newEndDate;
        if (isActive !== undefined) banner.isActive = isActive;

        await banner.save();

        res.json({
            success: true,
            message: 'Banner updated successfully',
            banner
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating banner',
            error: error.message
        });
    }
};

// @desc    Delete banner
// @route   DELETE /api/banners/:id
// @access  Private/Admin
const deleteBanner = async (req, res) => {
    try {
        const banner = await Banner.findById(req.params.id);
        if (!banner) {
            return res.status(404).json({
                success: false,
                message: 'Banner not found'
            });
        }

        await Banner.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Banner deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error deleting banner',
            error: error.message
        });
    }
};

// @desc    Toggle banner active status
// @route   PUT /api/banners/:id/toggle-active
// @access  Private/Admin
const toggleBannerStatus = async (req, res) => {
    try {
        const banner = await Banner.findById(req.params.id);
        if (!banner) {
            return res.status(404).json({
                success: false,
                message: 'Banner not found'
            });
        }

        banner.isActive = !banner.isActive;
        await banner.save();

        res.json({
            success: true,
            message: `Banner ${banner.isActive ? 'activated' : 'deactivated'} successfully`,
            banner: {
                id: banner._id,
                title: banner.title,
                isActive: banner.isActive
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error toggling banner status',
            error: error.message
        });
    }
};

// @desc    Update banner priority
// @route   PUT /api/banners/:id/priority
// @access  Private/Admin
const updateBannerPriority = async (req, res) => {
    try {
        const { priority } = req.body;

        if (priority === undefined || priority < 0) {
            return res.status(400).json({
                success: false,
                message: 'Priority must be a non-negative number'
            });
        }

        const banner = await Banner.findById(req.params.id);
        if (!banner) {
            return res.status(404).json({
                success: false,
                message: 'Banner not found'
            });
        }

        banner.priority = priority;
        await banner.save();

        res.json({
            success: true,
            message: 'Banner priority updated successfully',
            banner: {
                id: banner._id,
                title: banner.title,
                priority: banner.priority
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating banner priority',
            error: error.message
        });
    }
};

// @desc    Get banner statistics
// @route   GET /api/banners/stats
// @access  Private/Admin
const getBannerStats = async (req, res) => {
    try {
        const totalBanners = await Banner.countDocuments();
        const activeBanners = await Banner.countDocuments({ isActive: true });
        const inactiveBanners = await Banner.countDocuments({ isActive: false });

        const currentDate = new Date();
        const currentlyDisplayed = await Banner.countDocuments({
            isActive: true,
            startDate: { $lte: currentDate },
            endDate: { $gte: currentDate }
        });

        const expired = await Banner.countDocuments({
            endDate: { $lt: currentDate }
        });

        const scheduled = await Banner.countDocuments({
            startDate: { $gt: currentDate }
        });

        // Get banners by priority distribution
        const priorityDistribution = await Banner.aggregate([
            {
                $group: {
                    _id: '$priority',
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { _id: 1 }
            }
        ]);

        // Get recently created banners
        const recentBanners = await Banner.find()
            .sort({ createdAt: -1 })
            .limit(5)
            .select('title isActive priority createdAt');

        res.json({
            success: true,
            stats: {
                totalBanners,
                activeBanners,
                inactiveBanners,
                currentlyDisplayed,
                expired,
                scheduled,
                priorityDistribution,
                recentBanners
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching banner statistics',
            error: error.message
        });
    }
};

// @desc    Get banners expiring soon
// @route   GET /api/banners/expiring-soon
// @access  Private/Admin
const getExpiringSoonBanners = async (req, res) => {
    try {
        const { days = 7 } = req.query;
        const currentDate = new Date();
        const futureDate = new Date();
        futureDate.setDate(currentDate.getDate() + parseInt(days));

        const expiringSoonBanners = await Banner.find({
            isActive: true,
            endDate: {
                $gte: currentDate,
                $lte: futureDate
            }
        })
        .sort({ endDate: 1 })
        .select('title endDate priority');

        res.json({
            success: true,
            banners: expiringSoonBanners,
            count: expiringSoonBanners.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching expiring banners',
            error: error.message
        });
    }
};

// @desc    Bulk update banner status
// @route   PUT /api/banners/bulk-update
// @access  Private/Admin
const bulkUpdateBanners = async (req, res) => {
    try {
        const { bannerIds, action, value } = req.body;

        if (!bannerIds || !Array.isArray(bannerIds) || bannerIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Banner IDs array is required'
            });
        }

        let updateObject = {};
        let message = '';

        switch (action) {
            case 'activate':
                updateObject = { isActive: true };
                message = 'Banners activated successfully';
                break;
            case 'deactivate':
                updateObject = { isActive: false };
                message = 'Banners deactivated successfully';
                break;
            case 'priority':
                if (value === undefined || value < 0) {
                    return res.status(400).json({
                        success: false,
                        message: 'Priority value must be a non-negative number'
                    });
                }
                updateObject = { priority: value };
                message = 'Banner priorities updated successfully';
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid action. Use: activate, deactivate, or priority'
                });
        }

        const result = await Banner.updateMany(
            { _id: { $in: bannerIds } },
            updateObject
        );

        res.json({
            success: true,
            message,
            updated: result.modifiedCount
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating banners',
            error: error.message
        });
    }
};

module.exports = {
    getBanners,
    getActiveBanners,
    getBannerById,
    createBanner,
    updateBanner,
    deleteBanner,
    toggleBannerStatus,
    updateBannerPriority,
    getBannerStats,
    getExpiringSoonBanners,
    bulkUpdateBanners
};