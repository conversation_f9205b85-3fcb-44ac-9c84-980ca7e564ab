{"info": {"_postman_id": "user-api-collection-id", "name": "Dr Kumar User API Collection", "description": "Complete API collection for Dr Kumar User endpoints including authentication, cart management, orders, and product browsing.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "user_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "description": "User authentication endpoints - registration, login, email verification, password reset", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"phone\": \"**********\",\n  \"department\": \"General\",\n  \"role\": \"user\",\n  \"shippingAddress\": {\n    \"fullName\": \"<PERSON>\",\n    \"address\": \"123 Main Street\",\n    \"city\": \"New York\",\n    \"postalCode\": \"10001\",\n    \"country\": \"USA\"\n  },\n  \"paymentMethod\": \"credit_card\"\n}"}, "url": {"raw": "{{base_url}}/user/users/register", "host": ["{{base_url}}"], "path": ["user", "users", "register"]}, "description": "Register a new user account. Required fields: name, email, password, phone, paymentMethod, shippingAddress. Returns JWT token and user info. Email verification required."}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/user/users/login", "host": ["{{base_url}}"], "path": ["user", "users", "login"]}, "description": "Login with email and password. Returns JWT token and user information. Token should be used in Authorization header for protected routes."}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/users/verify-email/:token", "host": ["{{base_url}}"], "path": ["user", "users", "verify-email", ":token"], "variable": [{"key": "token", "value": "email_verification_token_here"}]}, "description": "Verify user email using the token sent to their email address during registration."}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/user/users/forgot-password", "host": ["{{base_url}}"], "path": ["user", "users", "forgot-password"]}, "description": "Request password reset. Sends reset token to user's email address."}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"NewSecurePassword123!\",\n  \"confirmPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/user/users/reset-password/:token", "host": ["{{base_url}}"], "path": ["user", "users", "reset-password", ":token"], "variable": [{"key": "token", "value": "password_reset_token_here"}]}, "description": "Reset password using the token received via email. Requires new password and confirmation."}}]}, {"name": "User Profile", "description": "User profile management endpoints - requires authentication", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "item": [{"name": "Get User Profile", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/users/profile", "host": ["{{base_url}}"], "path": ["user", "users", "profile"]}, "description": "Get current user's profile information including personal details and shipping address."}}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"1234567891\",\n  \"department\": \"Updated Department\",\n  \"shippingAddress\": {\n    \"fullName\": \"John Updated Doe\",\n    \"address\": \"456 Updated Street\",\n    \"city\": \"Updated City\",\n    \"postalCode\": \"10002\",\n    \"country\": \"USA\"\n  },\n  \"paymentMethod\": \"debit_card\"\n}"}, "url": {"raw": "{{base_url}}/user/users/profile", "host": ["{{base_url}}"], "path": ["user", "users", "profile"]}, "description": "Update user profile information. All fields are optional. Cannot update email directly."}}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"CurrentPassword123!\",\n  \"newPassword\": \"NewSecurePassword123!\",\n  \"confirmPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/user/users/change-password", "host": ["{{base_url}}"], "path": ["user", "users", "change-password"]}, "description": "Change user password. Requires current password for verification and new password with confirmation."}}]}, {"name": "Products", "description": "Product browsing and review endpoints - some public, some require authentication", "item": [{"name": "Get All Products", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/products?page=1&limit=10&category=electronics&minPrice=0&maxPrice=1000&inStock=true", "host": ["{{base_url}}"], "path": ["user", "products"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of products per page"}, {"key": "category", "value": "electronics", "description": "Filter by category"}, {"key": "minPrice", "value": "0", "description": "Minimum price filter"}, {"key": "maxPrice", "value": "1000", "description": "Maximum price filter"}, {"key": "inStock", "value": "true", "description": "Filter by stock availability"}]}, "description": "Get all products with filtering and pagination. Public endpoint. Supports filtering by category, price range, stock status, and search terms."}}, {"name": "Get Featured Products", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/products/featured", "host": ["{{base_url}}"], "path": ["user", "products", "featured"]}, "description": "Get all featured products. Public endpoint. Returns products marked as featured by admin."}}, {"name": "Get Product by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/products/:id", "host": ["{{base_url}}"], "path": ["user", "products", ":id"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Get detailed information about a specific product including reviews and ratings. Public endpoint."}}]}, {"name": "Product Reviews", "description": "Product review management - requires authentication", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "item": [{"name": "Add Product Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"comment\": \"Excellent product! Highly recommended.\"\n}"}, "url": {"raw": "{{base_url}}/user/products/:id/reviews", "host": ["{{base_url}}"], "path": ["user", "products", ":id", "reviews"], "variable": [{"key": "id", "value": "product_id_here"}]}, "description": "Add a review for a product. Requires authentication. Rating must be between 1-5, comment is optional."}}, {"name": "Update Product Review", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4,\n  \"comment\": \"Updated review: Good product with minor issues.\"\n}"}, "url": {"raw": "{{base_url}}/user/products/:id/reviews/:reviewId", "host": ["{{base_url}}"], "path": ["user", "products", ":id", "reviews", ":reviewId"], "variable": [{"key": "id", "value": "product_id_here"}, {"key": "reviewId", "value": "review_id_here"}]}, "description": "Update an existing product review. User can only update their own reviews."}}, {"name": "Delete Product Review", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/user/products/:id/reviews/:reviewId", "host": ["{{base_url}}"], "path": ["user", "products", ":id", "reviews", ":reviewId"], "variable": [{"key": "id", "value": "product_id_here"}, {"key": "reviewId", "value": "review_id_here"}]}, "description": "Delete a product review. User can only delete their own reviews."}}]}, {"name": "Shopping Cart", "description": "Shopping cart management endpoints - requires authentication", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_token}}", "type": "string"}]}, "item": [{"name": "Get Cart", "request": {"method": "GET", "url": {"raw": "{{base_url}}/user/cart", "host": ["{{base_url}}"], "path": ["user", "cart"]}, "description": "Get user's current cart with all items, quantities, and calculated totals."}}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"product_id_here\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{base_url}}/user/cart/add", "host": ["{{base_url}}"], "path": ["user", "cart", "add"]}, "description": "Add a product to the cart. If product already exists, quantity will be updated. Requires productId and quantity."}}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/user/cart/update/:itemId", "host": ["{{base_url}}"], "path": ["user", "cart", "update", ":itemId"], "variable": [{"key": "itemId", "value": "cart_item_id_here"}]}, "description": "Update quantity of a specific cart item. Use cart item ID, not product ID."}}, {"name": "Remove Item from Cart", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/user/cart/remove/:itemId", "host": ["{{base_url}}"], "path": ["user", "cart", "remove", ":itemId"], "variable": [{"key": "itemId", "value": "cart_item_id_here"}]}, "description": "Remove a specific item from the cart completely."}}, {"name": "Clear Cart", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/user/cart/clear", "host": ["{{base_url}}"], "path": ["user", "cart", "clear"]}, "description": "Remove all items from the cart. This action cannot be undone."}}]}]}