const ReturnRequest = require('../models/Return');
const Order = require('../models/Order');
const Product = require('../models/Products');
const User = require('../models/User');

// @desc    Create new return request
// @route   POST /api/returns
// @access  Private
const createReturnRequest = async (req, res) => {
    try {
        const { orderId, productId, reason, comments } = req.body;

        // Validate required fields
        if (!orderId || !productId || !reason) {
            return res.status(400).json({
                success: false,
                message: 'Order ID, Product ID, and reason are required'
            });
        }

        // Check if order exists and belongs to user
        const order = await Order.findById(orderId);
        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        if (order.user.toString() !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to create return for this order'
            });
        }

        // Check if order is delivered (can only return delivered orders)
        if (order.status !== 'delivered') {
            return res.status(400).json({
                success: false,
                message: 'Can only return delivered orders'
            });
        }

        // Check if product exists in the order
        const orderItem = order.items.find(item => item.product.toString() === productId);
        if (!orderItem) {
            return res.status(400).json({
                success: false,
                message: 'Product not found in this order'
            });
        }

        // Check if return request already exists for this order-product combination
        const existingReturn = await ReturnRequest.findOne({
            orderId,
            productId,
            userId: req.user.userId
        });

        if (existingReturn) {
            return res.status(400).json({
                success: false,
                message: 'Return request already exists for this product'
            });
        }

        // Check if order is within return window (e.g., 30 days)
        const returnWindow = 30; // days
        const orderDate = new Date(order.createdAt);
        const currentDate = new Date();
        const daysDifference = Math.floor((currentDate - orderDate) / (1000 * 60 * 60 * 24));

        if (daysDifference > returnWindow) {
            return res.status(400).json({
                success: false,
                message: `Return window has expired. Returns are allowed within ${returnWindow} days of order`
            });
        }

        // Create return request
        const returnRequest = new ReturnRequest({
            orderId,
            userId: req.user.userId,
            productId,
            reason,
            comments: comments || ''
        });

        await returnRequest.save();

        // Populate the return request with related data
        const populatedReturn = await ReturnRequest.findById(returnRequest._id)
            .populate('orderId', 'date totalAmount status')
            .populate('userId', 'name email')
            .populate('productId', 'name price images');

        res.status(201).json({
            success: true,
            message: 'Return request created successfully',
            returnRequest: populatedReturn
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error creating return request',
            error: error.message
        });
    }
};

// @desc    Get all return requests for logged-in user
// @route   GET /api/returns
// @access  Private
const getUserReturnRequests = async (req, res) => {
    try {
        const { page = 1, limit = 10, status } = req.query;

        // Build query object
        const query = { userId: req.user.userId };
        if (status) query.status = status;

        const returnRequests = await ReturnRequest.find(query)
            .populate('orderId', 'date totalAmount status')
            .populate('productId', 'name price images')
            .sort({ requestedAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await ReturnRequest.countDocuments(query);

        res.json({
            success: true,
            returnRequests,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching return requests',
            error: error.message
        });
    }
};

// @desc    Get return request by ID
// @route   GET /api/returns/:id
// @access  Private
const getReturnRequestById = async (req, res) => {
    try {
        const returnRequest = await ReturnRequest.findById(req.params.id)
            .populate('orderId', 'date totalAmount status items')
            .populate('userId', 'name email phone')
            .populate('productId', 'name price images description');

        if (!returnRequest) {
            return res.status(404).json({
                success: false,
                message: 'Return request not found'
            });
        }

        // Check if user is authorized to view this return request
        if (returnRequest.userId._id.toString() !== req.user.userId && 
            !['admin', 'employee'].includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to view this return request'
            });
        }

        res.json({
            success: true,
            returnRequest
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching return request',
            error: error.message
        });
    }
};

// @desc    Update return request (user can only update pending requests)
// @route   PUT /api/returns/:id
// @access  Private
const updateReturnRequest = async (req, res) => {
    try {
        const { reason, comments } = req.body;

        const returnRequest = await ReturnRequest.findById(req.params.id);
        if (!returnRequest) {
            return res.status(404).json({
                success: false,
                message: 'Return request not found'
            });
        }

        // Check if user is authorized to update this return request
        if (returnRequest.userId.toString() !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to update this return request'
            });
        }

        // Check if return request can be updated (only pending requests)
        if (returnRequest.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Can only update pending return requests'
            });
        }

        // Update fields if provided
        if (reason !== undefined) returnRequest.reason = reason;
        if (comments !== undefined) returnRequest.comments = comments;

        await returnRequest.save();

        const updatedReturn = await ReturnRequest.findById(returnRequest._id)
            .populate('orderId', 'date totalAmount status')
            .populate('productId', 'name price images');

        res.json({
            success: true,
            message: 'Return request updated successfully',
            returnRequest: updatedReturn
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating return request',
            error: error.message
        });
    }
};

// @desc    Cancel return request (user can only cancel pending requests)
// @route   DELETE /api/returns/:id
// @access  Private
const cancelReturnRequest = async (req, res) => {
    try {
        const returnRequest = await ReturnRequest.findById(req.params.id);
        if (!returnRequest) {
            return res.status(404).json({
                success: false,
                message: 'Return request not found'
            });
        }

        // Check if user is authorized to cancel this return request
        if (returnRequest.userId.toString() !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to cancel this return request'
            });
        }

        // Check if return request can be cancelled (only pending requests)
        if (returnRequest.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Can only cancel pending return requests'
            });
        }

        await ReturnRequest.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Return request cancelled successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error cancelling return request',
            error: error.message
        });
    }
};

// @desc    Get all return requests (Admin/Employee only)
// @route   GET /api/returns/admin/all
// @access  Private/Admin/Employee
const getAllReturnRequests = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            status, 
            userId, 
            productId, 
            dateFrom, 
            dateTo,
            search 
        } = req.query;

        // Build query object
        const query = {};
        if (status) query.status = status;
        if (userId) query.userId = userId;
        if (productId) query.productId = productId;
        if (dateFrom || dateTo) {
            query.requestedAt = {};
            if (dateFrom) query.requestedAt.$gte = new Date(dateFrom);
            if (dateTo) query.requestedAt.$lte = new Date(dateTo);
        }
        if (search) {
            query.$or = [
                { reason: { $regex: search, $options: 'i' } },
                { comments: { $regex: search, $options: 'i' } }
            ];
        }

        const returnRequests = await ReturnRequest.find(query)
            .populate('orderId', 'date totalAmount status')
            .populate('userId', 'name email phone')
            .populate('productId', 'name price images')
            .sort({ requestedAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await ReturnRequest.countDocuments(query);

        res.json({
            success: true,
            returnRequests,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching return requests',
            error: error.message
        });
    }
};

// @desc    Update return request status (Admin/Employee only)
// @route   PUT /api/returns/:id/status
// @access  Private/Admin/Employee
const updateReturnStatus = async (req, res) => {
    try {
        const { status, comments } = req.body;

        if (!['pending', 'approved', 'rejected', 'completed'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status value'
            });
        }

        const returnRequest = await ReturnRequest.findById(req.params.id);
        if (!returnRequest) {
            return res.status(404).json({
                success: false,
                message: 'Return request not found'
            });
        }

        // Validate status transition
        const validTransitions = {
            'pending': ['approved', 'rejected'],
            'approved': ['completed', 'rejected'],
            'rejected': ['approved'],
            'completed': [] // No transitions from completed
        };

        if (!validTransitions[returnRequest.status].includes(status)) {
            return res.status(400).json({
                success: false,
                message: `Cannot change status from ${returnRequest.status} to ${status}`
            });
        }

        returnRequest.status = status;
        if (comments) returnRequest.comments = comments;
        returnRequest.processedAt = new Date();

        await returnRequest.save();

        const updatedReturn = await ReturnRequest.findById(returnRequest._id)
            .populate('orderId', 'date totalAmount status')
            .populate('userId', 'name email')
            .populate('productId', 'name price images');

        res.json({
            success: true,
            message: 'Return request status updated successfully',
            returnRequest: updatedReturn
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating return status',
            error: error.message
        });
    }
};

// @desc    Get return request statistics (Admin/Employee only)
// @route   GET /api/returns/admin/stats
// @access  Private/Admin/Employee
const getReturnStats = async (req, res) => {
    try {
        const totalReturns = await ReturnRequest.countDocuments();
        const pendingReturns = await ReturnRequest.countDocuments({ status: 'pending' });
        const approvedReturns = await ReturnRequest.countDocuments({ status: 'approved' });
        const rejectedReturns = await ReturnRequest.countDocuments({ status: 'rejected' });
        const completedReturns = await ReturnRequest.countDocuments({ status: 'completed' });

        // Recent return requests
        const recentReturns = await ReturnRequest.find()
            .populate('userId', 'name email')
            .populate('productId', 'name price')
            .sort({ requestedAt: -1 })
            .limit(5);

        // Monthly return trends for the last 6 months
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const monthlyTrends = await ReturnRequest.aggregate([
            {
                $match: {
                    requestedAt: { $gte: sixMonthsAgo }
                }
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$requestedAt' },
                        month: { $month: '$requestedAt' }
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 }
            }
        ]);

        // Top return reasons
        const topReasons = await ReturnRequest.aggregate([
            {
                $group: {
                    _id: '$reason',
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { count: -1 }
            },
            {
                $limit: 10
            }
        ]);

        // Average processing time
        const processingTimes = await ReturnRequest.aggregate([
            {
                $match: { 
                    processedAt: { $exists: true },
                    status: { $in: ['approved', 'rejected', 'completed'] }
                }
            },
            {
                $addFields: {
                    processingTime: {
                        $divide: [
                            { $subtract: ['$processedAt', '$requestedAt'] },
                            1000 * 60 * 60 * 24 // Convert to days
                        ]
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    averageProcessingTime: { $avg: '$processingTime' }
                }
            }
        ]);

        const averageProcessingTime = processingTimes[0]?.averageProcessingTime || 0;

        res.json({
            success: true,
            stats: {
                totalReturns,
                statusDistribution: {
                    pending: pendingReturns,
                    approved: approvedReturns,
                    rejected: rejectedReturns,
                    completed: completedReturns
                },
                averageProcessingTime: Math.round(averageProcessingTime * 100) / 100, // Round to 2 decimal places
                recentReturns,
                monthlyTrends,
                topReasons
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching return statistics',
            error: error.message
        });
    }
};

// @desc    Get pending return requests (Admin/Employee only)
// @route   GET /api/returns/admin/pending
// @access  Private/Admin/Employee
const getPendingReturns = async (req, res) => {
    try {
        const { limit = 10 } = req.query;

        const pendingReturns = await ReturnRequest.find({ status: 'pending' })
            .populate('orderId', 'date totalAmount status')
            .populate('userId', 'name email phone')
            .populate('productId', 'name price images')
            .sort({ requestedAt: -1 })
            .limit(limit * 1);

        res.json({
            success: true,
            returnRequests: pendingReturns,
            count: pendingReturns.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching pending returns',
            error: error.message
        });
    }
};

// @desc    Bulk update return request status (Admin only)
// @route   PUT /api/returns/admin/bulk-update
// @access  Private/Admin
const bulkUpdateReturns = async (req, res) => {
    try {
        const { returnIds, status, comments } = req.body;

        if (!returnIds || !Array.isArray(returnIds) || returnIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Return IDs array is required'
            });
        }

        if (!['pending', 'approved', 'rejected', 'completed'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status value'
            });
        }

        const updateObject = { 
            status, 
            processedAt: new Date()
        };
        if (comments) updateObject.comments = comments;

        const result = await ReturnRequest.updateMany(
            { _id: { $in: returnIds } },
            updateObject
        );

        res.json({
            success: true,
            message: 'Return requests updated successfully',
            updated: result.modifiedCount
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating return requests',
            error: error.message
        });
    }
};

module.exports = {
    createReturnRequest,
    getUserReturnRequests,
    getReturnRequestById,
    updateReturnRequest,
    cancelReturnRequest,
    getAllReturnRequests,
    updateReturnStatus,
    getReturnStats,
    getPendingReturns,
    bulkUpdateReturns
};