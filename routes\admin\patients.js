const express = require('express');
const router = express.Router();
const {
    createPatient,
    getPatients,
    getPatientById,
    updatePatient,
    deletePatient,
    addHealthCondition,
    updateHealthCondition,
    deleteHealthCondition,
    scheduleAppointment,
    updateAppointmentStatus,
    getPatientsWithUpcomingAppointments,
    getPatientStats
} = require('../../controllers/patientController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin/employee access
router.use(protect);
router.use(authorize('admin', 'employee'));

// @route   POST /api/admin/patients
// @desc    Create new patient
// @access  Private/Admin/Employee
router.post('/', createPatient);

// @route   GET /api/admin/patients
// @desc    Get all patients (admin)
// @access  Private/Admin
router.get('/', require('../../controllers/patientController').getPatients);

// @route   GET /api/admin/patients/:id
// @desc    Get patient by ID (admin)
// @access  Private/Admin
router.get('/:id', require('../../controllers/patientController').getPatientById);

// @route   PUT /api/admin/patients/:id
// @desc    Update patient (admin)
// @access  Private/Admin
router.put('/:id', require('../../controllers/patientController').updatePatient);

// @route   DELETE /api/admin/patients/:id
// @desc    Delete patient (admin)
// @access  Private/Admin
router.delete('/:id', require('../../controllers/patientController').deletePatient);

// @route   POST /api/admin/patients/:id/health-conditions
// @desc    Add health condition to patient (admin)
// @access  Private/Admin
router.post('/:id/health-conditions', require('../../controllers/patientController').addHealthCondition);

// @route   PUT /api/admin/patients/:id/health-conditions/:conditionId
// @desc    Update health condition (admin)
// @access  Private/Admin
router.put('/:id/health-conditions/:conditionId', require('../../controllers/patientController').updateHealthCondition);

// @route   DELETE /api/admin/patients/:id/health-conditions/:conditionId
// @desc    Delete health condition (admin)
// @access  Private/Admin
router.delete('/:id/health-conditions/:conditionId', require('../../controllers/patientController').deleteHealthCondition);

// @route   POST /api/admin/patients/:id/appointments
// @desc    Schedule appointment for patient (admin)
// @access  Private/Admin
router.post('/:id/appointments', require('../../controllers/patientController').scheduleAppointment);

// @route   PUT /api/admin/patients/:id/appointments/:appointmentId/status
// @desc    Update appointment status (admin)
// @access  Private/Admin
router.put('/:id/appointments/:appointmentId/status', require('../../controllers/patientController').updateAppointmentStatus);

// @route   GET /api/admin/patients/upcoming-appointments
// @desc    Get patients with upcoming appointments (admin)
// @access  Private/Admin
router.get('/upcoming-appointments', require('../../controllers/patientController').getPatientsWithUpcomingAppointments);

// @route   GET /api/admin/patients/stats
// @desc    Get patient statistics (admin)
// @access  Private/Admin
router.get('/stats', require('../../controllers/patientController').getPatientStats);

module.exports = router;
