const express = require('express');
const router = express.Router();
const {
    getCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartSummary,
    validateCart,
    mergeCart,
    getCartCount
} = require('../../controllers/cartController');
const { protect } = require('../../middleware/auth');

// All cart routes require authentication
router.use(protect);

// @route   GET /api/user/cart
// @desc    Get user's cart
// @access  Private
router.get('/', require('../../controllers/cartController').getCart);

// @route   POST /api/user/cart/add
// @desc    Add item to cart
// @access  Private
router.post('/add', require('../../controllers/cartController').addToCart);

// @route   PUT /api/user/cart/update/:itemId
// @desc    Update cart item
// @access  Private
router.put('/update/:itemId', require('../../controllers/cartController').updateCartItem);

// @route   DELETE /api/user/cart/remove/:itemId
// @desc    Remove item from cart
// @access  Private
router.delete('/remove/:itemId', require('../../controllers/cartController').removeFromCart);

// @route   DELETE /api/user/cart/clear
// @desc    Clear cart
// @access  Private
router.delete('/clear', require('../../controllers/cartController').clearCart);

// @route   GET /api/user/cart/summary
// @desc    Get cart summary
// @access  Private
router.get('/summary', require('../../controllers/cartController').getCartSummary);

// @route   GET /api/user/cart/validate
// @desc    Validate cart
// @access  Private
router.get('/validate', require('../../controllers/cartController').validateCart);

// @route   POST /api/user/cart/merge
// @desc    Merge cart
// @access  Private
router.post('/merge', require('../../controllers/cartController').mergeCart);

// @route   GET /api/user/cart/count
// @desc    Get cart item count
// @access  Private
router.get('/count', require('../../controllers/cartController').getCartCount);

module.exports = router;
