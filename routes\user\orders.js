const express = require('express');
const router = express.Router();
const {
    createOrder,
    getUserOrders,
    getOrderById,
    cancelOrder,
    updateShippingAddress
} = require('../../controllers/orderController');
const { protect } = require('../../middleware/auth');

// All order routes require authentication
router.use(protect);

// @route   GET /api/user/orders
// @desc    Get user orders
// @access  Private
router.get('/', require('../../controllers/orderController').getUserOrders);

// @route   GET /api/user/orders/:id
// @desc    Get order by ID
// @access  Private
router.get('/:id', require('../../controllers/orderController').getOrderById);

// @route   POST /api/user/orders
// @desc    Create order
// @access  Private
router.post('/', require('../../controllers/orderController').createOrder);

// @route   PUT /api/user/orders/:id/cancel
// @desc    Cancel order
// @access  Private
router.put('/:id/cancel', require('../../controllers/orderController').cancelOrder);

// @route   PUT /api/user/orders/:id/shipping-address
// @desc    Update shipping address
// @access  Private
router.put('/:id/shipping-address', require('../../controllers/orderController').updateShippingAddress);

module.exports = router;
