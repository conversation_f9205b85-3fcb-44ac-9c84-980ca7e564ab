const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    description: {
        type: String,
        default: ''
    },
    createdAt: {
        type: Date,
        default: Date.now   
    },
    updatedat: {
        type: Date,
        default: Date.now
    }
});
CategorySchema.pre('save', function (next) {
    if (!this.isNew) {
        this.updatedat = Date.now();
    }
    next();
});
CategorySchema.pre('findOneAndUpdate', function (next) {
    this.set({ updatedat: Date.now() });
    next();
});
CategorySchema.pre('updateOne', function (next) {
    this.set({ updatedat: Date.now() });
    next();
});

module.exports = mongoose.model('Category', CategorySchema);