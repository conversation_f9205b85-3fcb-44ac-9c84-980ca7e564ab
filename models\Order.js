const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    items: [
        {
            product: { type: mongoose.Schema.Types.ObjectId, ref: 'Product', required: true },
            quantity: { type: Number, required: true, min: 1 },
            price: { type: Number, required: true, min: 0 }
        }
    ],
    date: { type: Date, default: Date.now },
    totalAmount: { type: Number, required: true, min: 0 },
    status: { type: String, enum: ['pending', 'processing', 'shipped', 'delivered', 'cancelled'], default: 'pending' },
    shippingAddress: {
        addressLine1: String,
        addressLine2: String,
        street: String,
        city: String,
        state: String,
        postalCode: String,
        country: {
            type: String,
            default: 'India'
        }
    },
    createdAt: { type: Date, default: Date.now },
    updatedat: { type: Date, default: Date.now }
});

orderSchema.pre('save', function (next) {
    if (!this.isNew) {
        this.updatedat = Date.now();
    }
    next();
});

orderSchema.pre('findOneAndUpdate', function (next) {
    this.set({ updatedat: Date.now() });
    next();
});

orderSchema.pre('updateOne', function (next) {
    this.set({ updatedat: Date.now() });
    next();
});

module.exports = mongoose.model('Order', orderSchema);