const express = require('express');
const router = express.Router();
const {
    getAllUsers,
    getUserById,
    updateUserStatus,
    deleteUser
} = require('../../controllers/userController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin access
router.use(protect);
router.use(authorize('admin'));

// @route   GET /api/admin/users
// @desc    Get all users (admin)
// @access  Private/Admin
router.get('/', require('../../controllers/userController').getAllUsers);

// @route   GET /api/admin/users/:id
// @desc    Get user by ID (admin)
// @access  Private/Admin
router.get('/:id', require('../../controllers/userController').getUserById);

// @route   PUT /api/admin/users/:id/status
// @desc    Update user status (admin)
// @access  Private/Admin
router.put('/:id/status', require('../../controllers/userController').updateUserStatus);

// @route   DELETE /api/admin/users/:id
// @desc    Delete user (admin)
// @access  Private/Admin
router.delete('/:id', require('../../controllers/userController').deleteUser);

module.exports = router;
