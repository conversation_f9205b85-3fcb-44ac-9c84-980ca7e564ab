const Complaint = require('../models/Complaint');
const User = require('../models/User');

// @desc    Create new complaint
// @route   POST /api/complaints
// @access  Private
const createComplaint = async (req, res) => {
    try {
        const { title, description, priority } = req.body;

        // Validate required fields
        if (!title || !description) {
            return res.status(400).json({
                success: false,
                message: 'Title and description are required'
            });
        }

        // Validate priority if provided
        if (priority && !['low', 'medium', 'high'].includes(priority)) {
            return res.status(400).json({
                success: false,
                message: 'Priority must be low, medium, or high'
            });
        }

        const complaint = new Complaint({
            title,
            description,
            priority: priority || 'medium',
            createdBy: req.user.userId
        });

        await complaint.save();

        // Populate the complaint with user details
        const populatedComplaint = await Complaint.findById(complaint._id)
            .populate('createdBy', 'name email');

        res.status(201).json({
            success: true,
            message: 'Complaint created successfully',
            complaint: populatedComplaint
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error creating complaint',
            error: error.message
        });
    }
};

// @desc    Get all complaints for logged-in user
// @route   GET /api/complaints
// @access  Private
const getUserComplaints = async (req, res) => {
    try {
        const { page = 1, limit = 10, status, priority } = req.query;

        // Build query object
        const query = { createdBy: req.user.userId };
        if (status) query.status = status;
        if (priority) query.priority = priority;

        const complaints = await Complaint.find(query)
            .populate('createdBy', 'name email')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Complaint.countDocuments(query);

        res.json({
            success: true,
            complaints,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching complaints',
            error: error.message
        });
    }
};

// @desc    Get complaint by ID
// @route   GET /api/complaints/:id
// @access  Private
const getComplaintById = async (req, res) => {
    try {
        const complaint = await Complaint.findById(req.params.id)
            .populate('createdBy', 'name email phone');

        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        // Check if user is authorized to view this complaint
        if (complaint.createdBy._id.toString() !== req.user.userId && 
            !['admin', 'employee'].includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to view this complaint'
            });
        }

        res.json({
            success: true,
            complaint
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching complaint',
            error: error.message
        });
    }
};

// @desc    Update complaint (user can only update their own complaints)
// @route   PUT /api/complaints/:id
// @access  Private
const updateComplaint = async (req, res) => {
    try {
        const { title, description, priority } = req.body;

        const complaint = await Complaint.findById(req.params.id);
        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        // Check if user is authorized to update this complaint
        if (complaint.createdBy.toString() !== req.user.userId) {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to update this complaint'
            });
        }

        // Check if complaint can be updated (only open and in_progress complaints)
        if (['resolved', 'closed'].includes(complaint.status)) {
            return res.status(400).json({
                success: false,
                message: 'Cannot update resolved or closed complaints'
            });
        }

        // Validate priority if provided
        if (priority && !['low', 'medium', 'high'].includes(priority)) {
            return res.status(400).json({
                success: false,
                message: 'Priority must be low, medium, or high'
            });
        }

        // Update fields if provided
        if (title !== undefined) complaint.title = title;
        if (description !== undefined) complaint.description = description;
        if (priority !== undefined) complaint.priority = priority;

        await complaint.save();

        const updatedComplaint = await Complaint.findById(complaint._id)
            .populate('createdBy', 'name email');

        res.json({
            success: true,
            message: 'Complaint updated successfully',
            complaint: updatedComplaint
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating complaint',
            error: error.message
        });
    }
};

// @desc    Delete complaint (user can only delete their own complaints)
// @route   DELETE /api/complaints/:id
// @access  Private
const deleteComplaint = async (req, res) => {
    try {
        const complaint = await Complaint.findById(req.params.id);
        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        // Check if user is authorized to delete this complaint
        if (complaint.createdBy.toString() !== req.user.userId && 
            req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Not authorized to delete this complaint'
            });
        }

        // Check if complaint can be deleted (only open complaints)
        if (complaint.status !== 'open') {
            return res.status(400).json({
                success: false,
                message: 'Can only delete open complaints'
            });
        }

        await Complaint.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Complaint deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error deleting complaint',
            error: error.message
        });
    }
};

// @desc    Get all complaints (Admin/Employee only)
// @route   GET /api/complaints/admin/all
// @access  Private/Admin/Employee
const getAllComplaints = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            status, 
            priority, 
            userId, 
            dateFrom, 
            dateTo,
            search 
        } = req.query;

        // Build query object
        const query = {};
        if (status) query.status = status;
        if (priority) query.priority = priority;
        if (userId) query.createdBy = userId;
        if (dateFrom || dateTo) {
            query.createdAt = {};
            if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
            if (dateTo) query.createdAt.$lte = new Date(dateTo);
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        const complaints = await Complaint.find(query)
            .populate('createdBy', 'name email phone')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Complaint.countDocuments(query);

        res.json({
            success: true,
            complaints,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching complaints',
            error: error.message
        });
    }
};

// @desc    Update complaint status (Admin/Employee only)
// @route   PUT /api/complaints/:id/status
// @access  Private/Admin/Employee
const updateComplaintStatus = async (req, res) => {
    try {
        const { status } = req.body;

        if (!['open', 'in_progress', 'resolved', 'closed'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status value'
            });
        }

        const complaint = await Complaint.findById(req.params.id);
        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        // Validate status transition
        const validTransitions = {
            'open': ['in_progress', 'resolved', 'closed'],
            'in_progress': ['resolved', 'closed', 'open'],
            'resolved': ['closed', 'open'],
            'closed': ['open']
        };

        if (!validTransitions[complaint.status].includes(status)) {
            return res.status(400).json({
                success: false,
                message: `Cannot change status from ${complaint.status} to ${status}`
            });
        }

        complaint.status = status;
        await complaint.save();

        const updatedComplaint = await Complaint.findById(complaint._id)
            .populate('createdBy', 'name email');

        res.json({
            success: true,
            message: 'Complaint status updated successfully',
            complaint: updatedComplaint
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating complaint status',
            error: error.message
        });
    }
};

// @desc    Update complaint priority (Admin/Employee only)
// @route   PUT /api/complaints/:id/priority
// @access  Private/Admin/Employee
const updateComplaintPriority = async (req, res) => {
    try {
        const { priority } = req.body;

        if (!['low', 'medium', 'high'].includes(priority)) {
            return res.status(400).json({
                success: false,
                message: 'Priority must be low, medium, or high'
            });
        }

        const complaint = await Complaint.findById(req.params.id);
        if (!complaint) {
            return res.status(404).json({
                success: false,
                message: 'Complaint not found'
            });
        }

        complaint.priority = priority;
        await complaint.save();

        res.json({
            success: true,
            message: 'Complaint priority updated successfully',
            complaint: {
                id: complaint._id,
                title: complaint.title,
                priority: complaint.priority
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating complaint priority',
            error: error.message
        });
    }
};

// @desc    Get complaint statistics (Admin/Employee only)
// @route   GET /api/complaints/admin/stats
// @access  Private/Admin/Employee
const getComplaintStats = async (req, res) => {
    try {
        const totalComplaints = await Complaint.countDocuments();
        const openComplaints = await Complaint.countDocuments({ status: 'open' });
        const inProgressComplaints = await Complaint.countDocuments({ status: 'in_progress' });
        const resolvedComplaints = await Complaint.countDocuments({ status: 'resolved' });
        const closedComplaints = await Complaint.countDocuments({ status: 'closed' });

        // Priority distribution
        const highPriorityComplaints = await Complaint.countDocuments({ priority: 'high' });
        const mediumPriorityComplaints = await Complaint.countDocuments({ priority: 'medium' });
        const lowPriorityComplaints = await Complaint.countDocuments({ priority: 'low' });

        // Recent complaints
        const recentComplaints = await Complaint.find()
            .populate('createdBy', 'name email')
            .sort({ createdAt: -1 })
            .limit(5);

        // Monthly complaint trends for the last 6 months
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const monthlyTrends = await Complaint.aggregate([
            {
                $match: {
                    createdAt: { $gte: sixMonthsAgo }
                }
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' }
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 }
            }
        ]);

        // Average resolution time (for resolved complaints)
        const resolutionTimes = await Complaint.aggregate([
            {
                $match: { status: 'resolved' }
            },
            {
                $addFields: {
                    resolutionTime: {
                        $divide: [
                            { $subtract: ['$updatedAt', '$createdAt'] },
                            1000 * 60 * 60 * 24 // Convert to days
                        ]
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    averageResolutionTime: { $avg: '$resolutionTime' }
                }
            }
        ]);

        const averageResolutionTime = resolutionTimes[0]?.averageResolutionTime || 0;

        res.json({
            success: true,
            stats: {
                totalComplaints,
                statusDistribution: {
                    open: openComplaints,
                    inProgress: inProgressComplaints,
                    resolved: resolvedComplaints,
                    closed: closedComplaints
                },
                priorityDistribution: {
                    high: highPriorityComplaints,
                    medium: mediumPriorityComplaints,
                    low: lowPriorityComplaints
                },
                averageResolutionTime: Math.round(averageResolutionTime * 100) / 100, // Round to 2 decimal places
                recentComplaints,
                monthlyTrends
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching complaint statistics',
            error: error.message
        });
    }
};

// @desc    Get urgent complaints (High priority and open/in_progress)
// @route   GET /api/complaints/admin/urgent
// @access  Private/Admin/Employee
const getUrgentComplaints = async (req, res) => {
    try {
        const { limit = 10 } = req.query;

        const urgentComplaints = await Complaint.find({
            priority: 'high',
            status: { $in: ['open', 'in_progress'] }
        })
        .populate('createdBy', 'name email phone')
        .sort({ createdAt: -1 })
        .limit(limit * 1);

        res.json({
            success: true,
            complaints: urgentComplaints,
            count: urgentComplaints.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching urgent complaints',
            error: error.message
        });
    }
};

// @desc    Bulk update complaint status (Admin only)
// @route   PUT /api/complaints/admin/bulk-update
// @access  Private/Admin
const bulkUpdateComplaints = async (req, res) => {
    try {
        const { complaintIds, action, value } = req.body;

        if (!complaintIds || !Array.isArray(complaintIds) || complaintIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Complaint IDs array is required'
            });
        }

        let updateObject = {};
        let message = '';

        switch (action) {
            case 'status':
                if (!['open', 'in_progress', 'resolved', 'closed'].includes(value)) {
                    return res.status(400).json({
                        success: false,
                        message: 'Invalid status value'
                    });
                }
                updateObject = { status: value };
                message = 'Complaint statuses updated successfully';
                break;
            case 'priority':
                if (!['low', 'medium', 'high'].includes(value)) {
                    return res.status(400).json({
                        success: false,
                        message: 'Invalid priority value'
                    });
                }
                updateObject = { priority: value };
                message = 'Complaint priorities updated successfully';
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid action. Use: status or priority'
                });
        }

        const result = await Complaint.updateMany(
            { _id: { $in: complaintIds } },
            updateObject
        );

        res.json({
            success: true,
            message,
            updated: result.modifiedCount
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating complaints',
            error: error.message
        });
    }
};

module.exports = {
    createComplaint,
    getUserComplaints,
    getComplaintById,
    updateComplaint,
    deleteComplaint,
    getAllComplaints,
    updateComplaintStatus,
    updateComplaintPriority,
    getComplaintStats,
    getUrgentComplaints,
    bulkUpdateComplaints
};