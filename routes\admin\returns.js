const express = require('express');
const router = express.Router();
const {
    getAllReturnRequests,
    updateReturnStatus,
    getReturnStats,
    getPendingReturns,
    bulkUpdateReturns
} = require('../../controllers/returnController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin/employee access
router.use(protect);
router.use(authorize('admin', 'employee'));

// @route   GET /api/admin/returns
// @desc    Get all return requests (admin)
// @access  Private/Admin
router.get('/', require('../../controllers/returnController').getAllReturnRequests);

// @route   GET /api/admin/returns/:id
// @desc    Get return request by ID (admin)
// @access  Private/Admin
router.get('/:id', require('../../controllers/returnController').getReturnRequestById);

// @route   PUT /api/admin/returns/:id/status
// @desc    Update return request status (admin)
// @access  Private/Admin
router.put('/:id/status', require('../../controllers/returnController').updateReturnStatus);

// @route   GET /api/admin/returns/stats
// @desc    Get return statistics (admin)
// @access  Private/Admin
router.get('/stats', require('../../controllers/returnController').getReturnStats);

// @route   GET /api/admin/returns/pending
// @desc    Get pending return requests (admin)
// @access  Private/Admin
router.get('/pending', require('../../controllers/returnController').getPendingReturns);

// @route   PUT /api/admin/returns/bulk-update
// @desc    Bulk update return requests (admin)
// @access  Private/Admin
router.put('/bulk-update', require('../../controllers/returnController').bulkUpdateReturns);

module.exports = router;
