# Dr Kumar API - Postman Collections

This repository contains comprehensive Postman collections for the Dr Kumar API, covering all admin and user endpoints with detailed descriptions and example requests.

## 📁 Collection Files

### Admin Collections
1. **<PERSON>-<PERSON>-Admin-API.postman_collection.json** - Main admin endpoints
   - Banner Management
   - Category Management  
   - Product Management
   - Order Management

2. **<PERSON><PERSON><PERSON>-Admin-API-Part2.postman_collection.json** - Additional admin endpoints
   - Complaint Management
   - Patient Management
   - Return Management
   - User Management

### User Collections
1. **Dr-<PERSON>-User-API.postman_collection.json** - Main user endpoints
   - Authentication (Register, Login, Email Verification, Password Reset)
   - User Profile Management
   - Product Browsing
   - Product Reviews
   - Shopping Cart Basic Operations

2. **Dr-<PERSON>-User-API-Part2.postman_collection.json** - Additional user endpoints
   - Advanced Cart Features
   - Order Management

## 🚀 Getting Started

### 1. Import Collections
1. Open Postman
2. Click "Import" button
3. Select all 4 JSON files
4. Collections will be imported with organized folders

### 2. Set Environment Variables
Create a new environment in Postman with these variables:

```
base_url: http://localhost:5000/api
admin_token: (will be set after admin login)
user_token: (will be set after user login)
```

### 3. Authentication Flow

#### For Admin Endpoints:
1. First register an admin user (you may need to manually set role to 'admin' in database)
2. Use the login endpoint to get admin token
3. Set the `admin_token` variable with the received JWT token
4. Admin collections will automatically use this token

#### For User Endpoints:
1. Use the "Register User" endpoint to create a new account
2. Verify email using the verification token (check your email or database)
3. Use the "Login User" endpoint to get user token
4. Set the `user_token` variable with the received JWT token
5. User collections will automatically use this token

## 📋 API Endpoint Overview

### Admin Endpoints (Require Admin Authentication)

#### Banner Management (`/api/admin/banners`)
- `POST /` - Create new banner
- `GET /` - Get all banners
- `GET /:id` - Get banner by ID
- `PUT /:id` - Update banner
- `DELETE /:id` - Delete banner
- `PUT /:id/toggle-active` - Toggle banner status
- `PUT /:id/priority` - Update banner priority
- `GET /stats` - Get banner statistics
- `GET /expiring-soon` - Get expiring banners
- `GET /active` - Get active banners
- `PUT /bulk-update` - Bulk update banners

#### Category Management (`/api/admin/categories`)
- `POST /` - Create category
- `GET /` - Get all categories
- `GET /:id` - Get category by ID
- `PUT /:id` - Update category
- `DELETE /:id` - Delete category
- `GET /productcount` - Get categories with product count
- `GET /category-products` - Get products by category
- `GET /stats` - Get category statistics
- `DELETE /bulk-delete` - Bulk delete categories

#### Product Management (`/api/admin/products`)
- `POST /` - Create product
- `GET /` - Get all products
- `GET /:id` - Get product by ID
- `PUT /:id` - Update product
- `DELETE /:id` - Delete product
- `PUT /:id/featured` - Toggle featured status
- `PUT /:id/stock` - Toggle stock status
- `GET /stats` - Get product statistics

#### Order Management (`/api/admin/orders`)
- `GET /` - Get all orders
- `GET /:id` - Get order by ID
- `PUT /:id/status` - Update order status
- `DELETE /:id` - Delete order
- `GET /stats` - Get order statistics

#### Complaint Management (`/api/admin/complaints`)
- `GET /` - Get all complaints
- `PUT /:id/status` - Update complaint status
- `PUT /:id/priority` - Update complaint priority
- `GET /stats` - Get complaint statistics
- `GET /urgent` - Get urgent complaints
- `PUT /bulk-update` - Bulk update complaints

#### Patient Management (`/api/admin/patients`)
- `POST /` - Create patient
- `GET /` - Get all patients
- `GET /:id` - Get patient by ID
- `PUT /:id` - Update patient
- `DELETE /:id` - Delete patient
- `POST /:id/health-conditions` - Add health condition
- `PUT /:id/health-conditions/:conditionId` - Update health condition
- `DELETE /:id/health-conditions/:conditionId` - Delete health condition
- `POST /:id/appointments` - Schedule appointment
- `PUT /:id/appointments/:appointmentId/status` - Update appointment status
- `GET /upcoming-appointments` - Get upcoming appointments
- `GET /stats` - Get patient statistics

### User Endpoints

#### Authentication (`/api/user/users`) - Public
- `POST /register` - Register new user
- `POST /login` - Login user
- `GET /verify-email/:token` - Verify email
- `POST /forgot-password` - Request password reset
- `POST /reset-password/:token` - Reset password

#### User Profile (Requires Authentication)
- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile
- `PUT /change-password` - Change password

#### Products (`/api/user/products`) - Public
- `GET /` - Get all products (with filtering)
- `GET /featured` - Get featured products
- `GET /:id` - Get product by ID

#### Product Reviews (Requires Authentication)
- `POST /:id/reviews` - Add product review
- `PUT /:id/reviews/:reviewId` - Update product review
- `DELETE /:id/reviews/:reviewId` - Delete product review

#### Shopping Cart (`/api/user/cart`) - Requires Authentication
- `GET /` - Get cart
- `POST /add` - Add item to cart
- `PUT /update/:itemId` - Update cart item
- `DELETE /remove/:itemId` - Remove item from cart
- `DELETE /clear` - Clear cart
- `GET /summary` - Get cart summary
- `GET /validate` - Validate cart
- `POST /merge` - Merge guest cart
- `GET /count` - Get cart item count

#### Orders (`/api/user/orders`) - Requires Authentication
- `POST /` - Create order
- `GET /` - Get user orders
- `GET /:id` - Get order by ID
- `PUT /:id/cancel` - Cancel order
- `PUT /:id/shipping-address` - Update shipping address

## 🔧 Request/Response Examples

### Authentication Example
```json
// Register Request
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phone": "1234567890",
  "paymentMethod": "credit_card",
  "shippingAddress": {
    "fullName": "John Doe",
    "address": "123 Main St",
    "city": "New York",
    "postalCode": "10001",
    "country": "USA"
  }
}

// Login Response
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

### Product Creation Example
```json
// Create Product Request (Admin)
{
  "name": "iPhone 15 Pro",
  "description": "Latest iPhone with advanced features",
  "categoryId": "category_id_here",
  "price": 999.99,
  "category": "Electronics",
  "inStock": true,
  "images": [
    {
      "url": "https://example.com/iphone15.jpg",
      "altText": "iPhone 15 Pro"
    }
  ],
  "isFeatured": false
}
```

### Cart Operations Example
```json
// Add to Cart Request
{
  "productId": "product_id_here",
  "quantity": 2
}

// Create Order Request
{
  "items": [
    {
      "product": "product_id_1",
      "quantity": 2,
      "price": 99.99
    }
  ],
  "shippingAddress": {
    "addressLine1": "123 Main Street",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "USA"
  }
}
```

## 🔐 Authentication Notes

- All protected routes require JWT token in Authorization header: `Bearer <token>`
- Admin routes require `admin` role
- Some admin routes also accept `employee` role (complaints, patients, returns)
- User routes require valid user authentication
- Tokens expire after a certain period (check your JWT configuration)

## 📝 Response Format

All API responses follow this standard format:

```json
{
  "success": true/false,
  "message": "Description of the result",
  "data": {}, // Response data (varies by endpoint)
  "error": "Error details (only in development mode)"
}
```

## 🚨 Error Handling

Common HTTP status codes used:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## 📞 Support

For questions about the API or these collections, please refer to the main project documentation or contact the development team.
