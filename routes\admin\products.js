const express = require('express');
const router = express.Router();
const {
    createProduct,
    updateProduct,
    deleteProduct,
    getProductStats,
    toggleFeaturedStatus,
    toggleStockStatus
} = require('../../controllers/productController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin access
router.use(protect);
router.use(authorize('admin'));

// @route   POST /api/admin/products
// @desc    Create new product
// @access  Private/Admin
router.post('/', createProduct);

// @route   PUT /api/admin/products/:id
// @desc    Update product
// @access  Private/Admin
router.put('/:id', updateProduct);

// @route   DELETE /api/admin/products/:id
// @desc    Delete product
// @access  Private/Admin
router.delete('/:id', deleteProduct);

// @route   GET /api/admin/products/stats
// @desc    Get product statistics
// @access  Private/Admin
router.get('/stats', getProductStats);

// @route   GET /api/admin/products
// @desc    Get all products (admin)
// @access  Private/Admin
router.get('/', require('../../controllers/productController').getProducts);

// @route   GET /api/admin/products/:id
// @desc    Get product by ID (admin)
// @access  Private/Admin
router.get('/:id', require('../../controllers/productController').getProductById);

// @route   PUT /api/admin/products/:id/featured
// @desc    Toggle product featured status
// @access  Private/Admin
router.put('/:id/featured', toggleFeaturedStatus);

// @route   PUT /api/admin/products/:id/stock
// @desc    Toggle product stock status
// @access  Private/Admin
router.put('/:id/stock', toggleStockStatus);

module.exports = router;
