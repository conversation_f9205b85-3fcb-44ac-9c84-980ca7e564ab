const express = require('express');
const dotenv = require('dotenv');
dotenv.config();
const morgan = require('morgan');
const connectDB = require('./config/db');
const cors = require('cors');
const app = express();

const port = process.env.PORT || 5000;

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(morgan('dev'));

// Multer setup for file uploads
// Run: npm install multer
const multer = require('multer');
const upload = multer({ dest: 'uploads/' }); // Default: saves files to uploads/ directory

// Admin routes
app.use('/api/admin/banners', require('./routes/admin/banners'));
app.use('/api/admin/categories', require('./routes/admin/categories'));
app.use('/api/admin/complaints', require('./routes/admin/complaints'));
app.use('/api/admin/orders', require('./routes/admin/orders'));
app.use('/api/admin/patients', require('./routes/admin/patients'));
app.use('/api/admin/products', require('./routes/admin/products'));
app.use('/api/admin/returns', require('./routes/admin/returns'));
app.use('/api/admin/users', require('./routes/admin/users'));

// User routes
app.use('/api/user/cart', require('./routes/user/cart'));
app.use('/api/user/orders', require('./routes/user/orders'));
app.use('/api/user/products', require('./routes/user/products'));
app.use('/api/user/users', require('./routes/user/users'));

// At the end, after all routes
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(err.statusCode || 500).json({
    success: false,
    message: err.message || 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? err : undefined
  });
});


const startServer = () => {
    connectDB().then((connection) => {
        console.log('Database connected successfully');
    }).catch(err => {
        console.error('Database connection error:', err);
    });
    app.listen(port, () => {
        console.log(`Server is running on port ${port}`);
    });
}   


startServer();
