{"info": {"_postman_id": "admin-api-part2-collection-id", "name": "Dr Kumar Admin API Collection - Part 2", "description": "Additional admin endpoints for Dr Kumar API including Complaints, Patients, Returns, and Users management. All routes require admin authentication.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}], "item": [{"name": "<PERSON><PERSON>", "description": "Complaint management endpoints for administrators and employees", "item": [{"name": "Get All Complaints", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/complaints", "host": ["{{base_url}}"], "path": ["admin", "complaints"]}, "description": "Retrieve all complaints with pagination and filtering options. Accessible by admin and employee roles."}}, {"name": "Update Complaint Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"resolved\",\n  \"adminNotes\": \"Issue has been resolved by technical team\"\n}"}, "url": {"raw": "{{base_url}}/admin/complaints/:id/status", "host": ["{{base_url}}"], "path": ["admin", "complaints", ":id", "status"], "variable": [{"key": "id", "value": "complaint_id_here"}]}, "description": "Update complaint status. Valid statuses: pending, in-progress, resolved, closed."}}, {"name": "Update Comp<PERSON>t Priority", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/admin/complaints/:id/priority", "host": ["{{base_url}}"], "path": ["admin", "complaints", ":id", "priority"], "variable": [{"key": "id", "value": "complaint_id_here"}]}, "description": "Update complaint priority. Valid priorities: low, medium, high."}}, {"name": "Get Complaint Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/complaints/stats", "host": ["{{base_url}}"], "path": ["admin", "complaints", "stats"]}, "description": "Get comprehensive statistics about complaints including total count, status breakdown, priority distribution, etc."}}, {"name": "<PERSON>mp<PERSON>ts", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/complaints/urgent", "host": ["{{base_url}}"], "path": ["admin", "complaints", "urgent"]}, "description": "Get all complaints marked as high priority or urgent that need immediate attention."}}, {"name": "Bulk Update Complaints", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"complaintIds\": [\"complaint_id_1\", \"complaint_id_2\"],\n  \"updates\": {\n    \"status\": \"in-progress\",\n    \"priority\": \"medium\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/complaints/bulk-update", "host": ["{{base_url}}"], "path": ["admin", "complaints", "bulk-update"]}, "description": "Update multiple complaints at once. Provide array of complaint IDs and the updates to apply."}}]}, {"name": "Admin Patients", "description": "Patient management endpoints for administrators and employees", "item": [{"name": "Create Patient", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"**********\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"male\",\n  \"address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10001\",\n    \"country\": \"USA\"\n  },\n  \"emergencyContact\": {\n    \"name\": \"<PERSON>\",\n    \"phone\": \"**********\",\n    \"relationship\": \"spouse\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/patients", "host": ["{{base_url}}"], "path": ["admin", "patients"]}, "description": "Create a new patient record. Required fields: name, email, phone, dateOfBirth, gender."}}, {"name": "Get All Patients", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/patients", "host": ["{{base_url}}"], "path": ["admin", "patients"]}, "description": "Retrieve all patients with pagination and filtering options."}}, {"name": "Get Patient by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/patients/:id", "host": ["{{base_url}}"], "path": ["admin", "patients", ":id"], "variable": [{"key": "id", "value": "patient_id_here"}]}, "description": "Get a specific patient by ID with full details including health conditions and appointments."}}, {"name": "Update Patient", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phone\": \"**********\",\n  \"address\": {\n    \"street\": \"456 Updated St\",\n    \"city\": \"Updated City\",\n    \"state\": \"NY\",\n    \"zipCode\": \"10002\",\n    \"country\": \"USA\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/patients/:id", "host": ["{{base_url}}"], "path": ["admin", "patients", ":id"], "variable": [{"key": "id", "value": "patient_id_here"}]}, "description": "Update patient information. All fields are optional."}}, {"name": "Delete Patient", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/admin/patients/:id", "host": ["{{base_url}}"], "path": ["admin", "patients", ":id"], "variable": [{"key": "id", "value": "patient_id_here"}]}, "description": "Delete a patient record by ID. This action is irreversible."}}, {"name": "Get Patient Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/patients/stats", "host": ["{{base_url}}"], "path": ["admin", "patients", "stats"]}, "description": "Get comprehensive statistics about patients including total count, age distribution, appointment statistics, etc."}}]}, {"name": "Admin Returns", "description": "Return request management endpoints for administrators and employees", "item": [{"name": "Get All Return Requests", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/returns", "host": ["{{base_url}}"], "path": ["admin", "returns"]}, "description": "Retrieve all return requests with pagination and filtering options."}}, {"name": "Get Return Request by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/returns/:id", "host": ["{{base_url}}"], "path": ["admin", "returns", ":id"], "variable": [{"key": "id", "value": "return_id_here"}]}, "description": "Get detailed information about a specific return request."}}, {"name": "Update Return Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"adminNotes\": \"Return approved, refund will be processed\"\n}"}, "url": {"raw": "{{base_url}}/admin/returns/:id/status", "host": ["{{base_url}}"], "path": ["admin", "returns", ":id", "status"], "variable": [{"key": "id", "value": "return_id_here"}]}, "description": "Update return request status. Valid statuses: pending, approved, rejected, processed."}}, {"name": "Get Return Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/returns/stats", "host": ["{{base_url}}"], "path": ["admin", "returns", "stats"]}, "description": "Get comprehensive statistics about return requests including total count, status breakdown, etc."}}, {"name": "Get Pending Returns", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/returns/pending", "host": ["{{base_url}}"], "path": ["admin", "returns", "pending"]}, "description": "Get all pending return requests that need admin attention."}}, {"name": "Bulk Update Returns", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"returnIds\": [\"return_id_1\", \"return_id_2\"],\n  \"updates\": {\n    \"status\": \"approved\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/returns/bulk-update", "host": ["{{base_url}}"], "path": ["admin", "returns", "bulk-update"]}, "description": "Update multiple return requests at once. Provide array of return IDs and the updates to apply."}}]}, {"name": "Admin Users", "description": "User management endpoints for administrators", "item": [{"name": "Get All Users", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/users", "host": ["{{base_url}}"], "path": ["admin", "users"]}, "description": "Retrieve all users with pagination and filtering options."}}, {"name": "Get User by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/users/:id", "host": ["{{base_url}}"], "path": ["admin", "users", ":id"], "variable": [{"key": "id", "value": "user_id_here"}]}, "description": "Get detailed information about a specific user."}}, {"name": "Update User Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"banned\",\n  \"reason\": \"Violation of terms of service\"\n}"}, "url": {"raw": "{{base_url}}/admin/users/:id/status", "host": ["{{base_url}}"], "path": ["admin", "users", ":id", "status"], "variable": [{"key": "id", "value": "user_id_here"}]}, "description": "Update user status. Valid statuses: active, inactive, banned."}}, {"name": "Delete User", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/admin/users/:id", "host": ["{{base_url}}"], "path": ["admin", "users", ":id"], "variable": [{"key": "id", "value": "user_id_here"}]}, "description": "Delete a user account. This action is irreversible and will also delete associated data."}}]}]}